# Fieldwire Test Automation Framework

A concise web automation testing framework for Fieldwire staging application using Playwright and TypeScript.

## Quick Start

### Prerequisites
```bash
npm install
npm run install:browsers
```

### Run Tests
```bash
npm test                    # All tests
npm run test:headed         # With browser visible
npm run test:debug          # Debug mode
npx playwright test --list  # List available tests
```

### Build
```bash
npm run build              # Compile TypeScript
```

## Test Coverage

### Current Tests (2 total)
- **Authentication**: `tests/auth/login.spec.ts`
  - Two-step login flow with valid credentials
  - Projects page verification
- **Task Management**: `tests/tasks/task-creation-journey.spec.ts`
  - End-to-end task creation workflow
  - Modal interactions (priority, attachments, checklists)
  - Kanban board verification

### Test Data Strategy
- **Inline data** with timestamp-based unique identifiers
- **No external dependencies** or data files
- **Test isolation** through unique naming

## Framework Architecture

### Page Object Model (Locator-Only)
```typescript
// ✅ Locators defined in Page Objects
readonly taskModal = page.locator('[data-e2e="task-view-edit-modal"]');

// ✅ Direct interactions in tests
await page.locator('[data-e2e="create-new-task"]').click();
await expect(taskModal).toBeVisible();
```

### Key Design Principles
- **Fail-fast**: Tests expect specific elements, fail immediately if missing
- **Data-e2e selectors**: Prioritize `[data-e2e="..."]` over CSS/text selectors
- **Direct Playwright interactions**: No abstraction layers hiding test intent
- **TypeScript-only**: No JavaScript artifacts

## Configuration

### Test Settings
- **Base URL**: `https://staging.fieldwire.com`
- **Timeout**: 30s per action
- **Retries**: 2 on CI, 0 locally
- **Browser**: Chromium only
- **Trace**: Enabled for debugging

### Credentials
```typescript
const credentials = {
  email: '<EMAIL>',
  password: 'FwTakeh0mePass13'
};
```

## File Structure

```
src/
├── pages/           # Page Object locators only
├── types/           # Essential interfaces (TestCredentials, WaitOptions)
└── utils/           # AuthUtils (login method only)

tests/
├── auth/            # Login test
├── tasks/           # Task creation test
└── setup/           # Test fixtures and configuration
```

## Debug and Troubleshooting

### Common Commands
```bash
npx playwright test --trace on              # Record traces
npx playwright show-trace                   # View traces
npx playwright test --headed --debug        # Step-through debugging
npm run report                              # View test results
```

### Test Execution Options
```bash
# Specific test
npx playwright test tests/auth/login.spec.ts

# With custom timeout
npx playwright test --timeout 60000

# UI mode
npm run test:ui
```

## Design Decisions


### Trade-offs Made
- **Verbose test code** vs **clear test intent** → Chose clarity
- **Role-based locators** vs **data-e2e** → Chose resilience over user facing locators

## Future Enhancements

### Would Add With More Time
- **Negative test cases**: Invalid credentials, error handling
- **Additional workflows**: Task editing, form creation, file uploads
- **Performance testing**: Load time assertions
- **Multi-environment support**: Local, staging, production configs
- **Visual regression**: Screenshot comparisons
- **CI/CD integration**: Automated execution on PRs

### Nice to Haves
- **Test data cleanup**: Automated removal of test-created tasks
- **Environment validation**: Pre-flight checks for staging availability
- **Retry mechanisms**: Handle flaky network calls gracefully
- **Monitoring/alerting**: Slack notifications for test failures
- **IDE setup guide**: VS Code extensions and debugging configuration
- **Secrets management**: Environment variables for credentials
- **Accessibility testing**: Screen reader and keyboard navigation
- **Mobile viewport testing**: Responsive design validation
- **Load testing**: Performance under stress scenarios
- **Test scheduling**: Nightly runs and PR automation
- **Contribution guidelines**: Standards for adding new tests
- **Troubleshooting FAQ**: Common issues and solutions

### Scalability Considerations
```typescript
// Environment abstraction
const config = {
  staging: { baseURL: 'https://staging.fieldwire.com' },
  prod: { baseURL: 'https://app.fieldwire.com' }
};

// Component-based locators
class TaskModal {
  readonly priority = page.locator('[data-e2e="task-priority"]');
  readonly attachments = page.locator('[data-e2e="attachments"]');
}
```

## Key Files

- **`playwright.config.ts`**: Main configuration
- **`tests/setup/test-setup.ts`**: Test fixtures and page objects
- **`src/pages/`**: Locator definitions only
- **`src/utils/AuthUtils.ts`**: Login functionality
- **Test credentials**: Hardcoded in AuthUtils and login test
