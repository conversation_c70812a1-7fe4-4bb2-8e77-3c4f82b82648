import { test, expect } from '../setup/test-setup';

test.describe('Forms Permissions Management', () => {
  test.beforeEach(async ({ authenticatedPage, projectsPage, formsPage, page }) => {
    await projectsPage.verifyProjectsPageLoaded();

    const timestamp = Date.now();
    const projectName = `Forms Test Project ${timestamp}`;
    await projectsPage.createNewProject(projectName);

    await projectsPage.modal.waitFor({ state: 'hidden', timeout: 10000 }).catch(() => {});

    await formsPage.formsNavLink.click();
    await page.waitForURL('**/forms**');
  });

  test('should verify forms page functionality', async ({ formsPage }) => {
    test.info().annotations.push({ type: 'tag', description: 'forms-functionality' });

    await test.step('Verify forms page loads with expected elements', async () => {
      await formsPage.verifyFormsPageLoaded();
      await expect(formsPage.formsActionsButton).toBeVisible();
      await expect(formsPage.formsHeader).toBeVisible();
      await expect(formsPage.myFormsFilter).toBeVisible();
      await expect(formsPage.allFormsFilter).toBeVisible();
    });

    await test.step('Verify forms page sidebar elements', async () => {
      await expect(formsPage.myFormsFilter).toBeVisible();
      await expect(formsPage.allFormsFilter).toBeVisible();
    });

    await test.step('Verify forms page empty state', async () => {
      if (await formsPage.isEmptyState()) {
        await expect(formsPage.emptyState).toBeVisible();
      } else {
        await expect(formsPage.formsTab).toBeVisible();
      }
    });

    await test.step('Verify forms navigation and UI elements', async () => {
      await expect(formsPage.formsMenu).toBeVisible();
      await expect(formsPage.formsTab).toBeVisible();
      await expect(formsPage.searchInput).toBeVisible();
    });
  });
});
